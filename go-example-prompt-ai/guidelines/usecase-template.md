
---

# 🧩 Template: Creating a New Use Case for a Domain (Clean Architecture + DDD)

> ✅ Use this template when creating a new **use case** (e.g., `create`, `update`) for a specific **domain** or **subdomain** (e.g., `products`, `catalog/products`, `billing/invoices`).
> 
> ✳️ It enforces **Domain-Driven Design (DDD)** and **Clean Architecture** principles, supports **modularity**, and is structured for **feature-level reuse**.

---



## 🔧 Template Variables

|Variable|Description|
|---|---|
|`<domain>`|The domain or subdomain (e.g., `products`, `catalog/products`)|
|`<usecase_name>`|The specific use case (e.g., `create`, `update`, `delete`)|

> 📝 **Note**: If the domain has subdomains, reflect that in the folder hierarchy under `usecase/`, e.g., `usecase/catalog/products/create`.



---

## 🏗 Folder Structure


```bash
usecase/
└── <domain>/                          # Domain module (e.g., products, catalog/products)
    ├── service/                       # Application-level orchestrator
    │   └── <entity>_service.go
    ├── repository/                    # Repository interface (output port)
    │   └── repository.go
    ├── validator/                     # Shared domain validation rules
    │   └── validator.go
    ├── infra/                         # Infrastructure implementation (bun, etc.)
    │   └── bunrepo/                   # Bun-based DB logic
    │       ├── entity.go              # DB schema struct (with tags)
    │       ├── mapper.go              # Converts between entity and domain
    │       └── repository.go          # Bun repository implementation
    └── <usecase_name>/               # e.g., create/, update/
        ├── in/                        # Input layer (application port)
        │   ├── input.go               # Input DTO
        │   ├── output.go              # Output DTO
        │   ├── inport.go              # Use case interface
        │   ├── interactor.go          # Use case logic (implements inport)
        │   └── validator/             # Input validation using go-playground/validator
        │       └── validator.go
        ├── out/                       # Output layer (presenter/side effects)
        │   ├── output.go              # Presenter response DTO
        │   ├── outport.go             # Presenter interface (output port)
        │   └── presenter.go           # Presenter implementation

```



## 🧠 Guidelines for Implementation

### `in/interactor.go`

- Implements the `inport` interface.
    
- Contains all core business logic.
    
- Validates input, invokes domain rules, and returns the output result to be used by the `outport`.
    

### `inport.go`

- Interface for the use case contract.
    
- Consumed by the service layer.
    

### `validator/validator.go`

- Uses `github.com/go-playground/validator`.
    
- Validates input struct before executing business logic.
    

### `outport.go`

- Defines the contract for result formatting, notification, or side-effects.
    

### `out/presenter.go`

- Implements the `outport` interface.
    
- Typically transforms use case output from the inport into a format suitable for delivery.
    

### `service/<entity>_service.go`

- Orchestrates use case execution (inport/outport) interactions.
    
- Connects use cases to controller-level entry points.

**Service example:**

```go
type ProductService struct {
	createInport  createin.Inport
	createOutport createout.Outport
}


func (s *ProductService) CreateProduct(input createin.Input) (createout.ViewModel, error) {
	// Execute the use case through the inport
	inOutput, err := s.createInport.Execute(input)
	
	// Present the result through the outport
	return s.createOutport.Present(inOutput, err)
}
```


---

## 🏷 Naming Conventions

| Item                 | Convention              |
| -------------------- | ----------------------- |
| Use case interface   | `<Verb><Entity>UseCase` |
| Interactor struct    | `Interactor`            |
| Input/Output structs | `Input`, `Output`       |
| Presenter interface  | `Presenter`             |
| Repository interface | `<Entity>Repository`    |
| Validator struct     | `<Entity>Validator`     |
| inport/outport interfaces| `<Verb><Entity>Inport`, `<Verb><Entity>Outport` |




---

## 🧭 Why This Structure?

- ✅ **Feature modularity**: Each use case is self-contained and easy to copy or reuse.
    
- ✅ **Clean boundaries**: No cross-contamination between domain, application, and infrastructure.
    
- ✅ **High testability**: Each component (interactor, validator, presenter) is easily unit tested.
    
- ✅ **Future-proof**: Supports multiple delivery mechanisms (HTTP, gRPC, CLI) via the service layer.
    
- ✅ **Portability**: The entire `<domain>` folder can be moved across projects with minimal friction — including subdomains.
    

---

## 📝 Final Notes

> ✅ **When creating a new use case:**
> 
> 1. **Specify the `<domain>` and `<usecase_name>`** — subdomains should be reflected in the path.
>     
> 2. **Check existing use cases** in the project to follow the same structure and naming patterns.
>     
> 3. **Use this template** to ensure consistency, scalability, and architectural clarity.
>     

---