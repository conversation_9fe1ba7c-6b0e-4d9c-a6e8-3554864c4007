Follow the guidelines in `guidelines/usecase-template.md`


Generate the following use cases for the domain `calatog/products`


# `create` 

Create a new product

Input:
- `name` (required, string)
- `description` (required, string)
- `price` (required, float64)
- `quantity` (required, int)
- `category` (required, string)
- `tags` (optional, []string)
- `images` (optional, []string)
- `attributes` (optional, map[string]string)

Output:
- `product` (Product)

# `update`

Update an existing product

Input:
- `id` (required, string)
- `name` (optional, string)
- `description` (optional, string)
- `price` (optional, float64)
- `quantity` (optional, int)
- `category` (optional, string)
- `tags` (optional, []string)
- `images` (optional, []string)
- `attributes` (optional, map[string]string)
Output:
- `product` (Product)

# `delete`

Delete an existing product

Input:
- `id` (required, string)

Output:
- `success` (bool)

# `get`

Get an existing product

Input:
- `id` (required, string)

Output:
- `product` (Product)

# `list`

List all products

Input:
- `page` (optional, int)
- `limit` (optional, int)