package out

import (
	"demo/usecase/catalog/products/create/in"
)

// Presenter implements the create product result presentation logic
type Presenter struct{}

// NewPresenter creates a new create product presenter
func NewPresenter() *Presenter {
	return &Presenter{}
}

// Present implements the Outport interface
func (p *Presenter) Present(output in.Output, err error) (ViewModel, error) {
	if err != nil {
		return ViewModel{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	return ViewModel{
		Success: true,
		Message: "Product created successfully",
		Product: output.Product,
	}, nil
}
