package in

import (
	"context"
	"crypto/rand"
	"demo/usecase/catalog/products"
	"demo/usecase/catalog/products/repository"
	"demo/usecase/catalog/products/validator"
	"encoding/hex"
	"fmt"
	"time"
)

// Interactor implements the create product use case business logic
type Interactor struct {
	repo      repository.ProductRepository
	validator *validator.ProductValidator
}

// NewInteractor creates a new create product interactor
func NewInteractor(repo repository.ProductRepository, validator *validator.ProductValidator) *Interactor {
	return &Interactor{
		repo:      repo,
		validator: validator,
	}
}

// Execute implements the Inport interface
func (i *Interactor) Execute(input Input) (Output, error) {
	// Validate input using domain validator
	if err := i.validateInput(input); err != nil {
		return Output{}, fmt.Errorf("validation failed: %w", err)
	}

	// Generate unique ID for the product
	productID := generateID()

	// Create domain entity
	product := &products.Product{
		ID:          productID,
		Name:        input.Name,
		Description: input.Description,
		Price:       input.Price,
		Quantity:    input.Quantity,
		Category:    input.Category,
		Tags:        input.Tags,
		Images:      input.Images,
		Attributes:  input.Attributes,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Persist the product
	ctx := context.Background()
	if err := i.repo.Create(ctx, product); err != nil {
		return Output{}, fmt.Errorf("failed to create product: %w", err)
	}

	return Output{Product: product}, nil
}

// validateInput validates the input using domain validation rules
func (i *Interactor) validateInput(input Input) error {
	if err := i.validator.ValidateProductName(input.Name); err != nil {
		return err
	}

	if err := i.validator.ValidateProductDescription(input.Description); err != nil {
		return err
	}

	if err := i.validator.ValidateProductPrice(input.Price); err != nil {
		return err
	}

	if err := i.validator.ValidateProductQuantity(input.Quantity); err != nil {
		return err
	}

	if err := i.validator.ValidateProductCategory(input.Category); err != nil {
		return err
	}

	return nil
}

// generateID generates a simple unique ID for products
func generateID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
