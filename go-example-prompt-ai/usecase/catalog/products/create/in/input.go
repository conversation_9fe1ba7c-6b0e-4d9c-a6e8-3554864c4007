package in

// Input represents the input data for creating a product
type Input struct {
	Name        string            `json:"name" validate:"required,min=2,max=255"`
	Description string            `json:"description" validate:"required,min=10,max=2000"`
	Price       float64           `json:"price" validate:"required,min=0,max=999999.99"`
	Quantity    int               `json:"quantity" validate:"required,min=0,max=1000000"`
	Category    string            `json:"category" validate:"required,min=2,max=100"`
	Tags        []string          `json:"tags,omitempty" validate:"omitempty,dive,min=1,max=50"`
	Images      []string          `json:"images,omitempty" validate:"omitempty,dive,url"`
	Attributes  map[string]string `json:"attributes,omitempty" validate:"omitempty,dive,keys,min=1,max=50,endkeys,min=1,max=255"`
}
