package validator

import (
	"demo/usecase/catalog/products/create/in"

	"github.com/go-playground/validator/v10"
)

// Validator handles input validation for create product use case
type Validator struct {
	validate *validator.Validate
}

// NewValidator creates a new input validator
func NewValidator() *Validator {
	return &Validator{
		validate: validator.New(),
	}
}

// ValidateInput validates the create product input
func (v *Validator) ValidateInput(input in.Input) error {
	return v.validate.Struct(input)
}
