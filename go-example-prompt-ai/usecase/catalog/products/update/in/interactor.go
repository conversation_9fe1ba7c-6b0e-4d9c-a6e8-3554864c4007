package in

import (
	"context"
	"demo/usecase/catalog/products"
	"demo/usecase/catalog/products/repository"
	"demo/usecase/catalog/products/validator"
	"fmt"
	"time"
)

// Interactor implements the update product use case business logic
type Interactor struct {
	repo      repository.ProductRepository
	validator *validator.ProductValidator
}

// NewInteractor creates a new update product interactor
func NewInteractor(repo repository.ProductRepository, validator *validator.ProductValidator) *Interactor {
	return &Interactor{
		repo:      repo,
		validator: validator,
	}
}

// Execute implements the Inport interface
func (i *Interactor) Execute(input Input) (Output, error) {
	// Validate input using domain validator
	if err := i.validateInput(input); err != nil {
		return Output{}, fmt.Errorf("validation failed: %w", err)
	}

	ctx := context.Background()

	// Check if product exists
	existingProduct, err := i.repo.GetByID(ctx, input.ID)
	if err != nil {
		return Output{}, fmt.Errorf("failed to get product: %w", err)
	}

	// Update only the fields that are provided
	updatedProduct := i.mergeUpdates(existingProduct, input)
	updatedProduct.UpdatedAt = time.Now()

	// Persist the updated product
	if err := i.repo.Update(ctx, updatedProduct); err != nil {
		return Output{}, fmt.Errorf("failed to update product: %w", err)
	}

	return Output{Product: updatedProduct}, nil
}

// validateInput validates the input using domain validation rules
func (i *Interactor) validateInput(input Input) error {
	if err := i.validator.ValidateProductID(input.ID); err != nil {
		return err
	}

	if input.Name != nil {
		if err := i.validator.ValidateProductName(*input.Name); err != nil {
			return err
		}
	}

	if input.Description != nil {
		if err := i.validator.ValidateProductDescription(*input.Description); err != nil {
			return err
		}
	}

	if input.Price != nil {
		if err := i.validator.ValidateProductPrice(*input.Price); err != nil {
			return err
		}
	}

	if input.Quantity != nil {
		if err := i.validator.ValidateProductQuantity(*input.Quantity); err != nil {
			return err
		}
	}

	if input.Category != nil {
		if err := i.validator.ValidateProductCategory(*input.Category); err != nil {
			return err
		}
	}

	return nil
}

// mergeUpdates merges the update input with the existing product
func (i *Interactor) mergeUpdates(existing *products.Product, input Input) *products.Product {
	updated := *existing // Copy the existing product

	if input.Name != nil {
		updated.Name = *input.Name
	}
	if input.Description != nil {
		updated.Description = *input.Description
	}
	if input.Price != nil {
		updated.Price = *input.Price
	}
	if input.Quantity != nil {
		updated.Quantity = *input.Quantity
	}
	if input.Category != nil {
		updated.Category = *input.Category
	}
	if input.Tags != nil {
		updated.Tags = *input.Tags
	}
	if input.Images != nil {
		updated.Images = *input.Images
	}
	if input.Attributes != nil {
		updated.Attributes = *input.Attributes
	}

	return &updated
}
