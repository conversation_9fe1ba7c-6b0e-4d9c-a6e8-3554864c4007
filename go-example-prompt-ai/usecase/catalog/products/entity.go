package products

import (
	"time"
)

// Product represents the core domain entity for a product
type Product struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Price       float64           `json:"price"`
	Quantity    int               `json:"quantity"`
	Category    string            `json:"category"`
	Tags        []string          `json:"tags,omitempty"`
	Images      []string          `json:"images,omitempty"`
	Attributes  map[string]string `json:"attributes,omitempty"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}
