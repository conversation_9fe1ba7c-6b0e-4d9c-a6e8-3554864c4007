package in

import (
	"context"
	"demo/usecase/catalog/products/repository"
	"fmt"
)

// Interactor implements the list products use case business logic
type Interactor struct {
	repo repository.ProductRepository
}

// NewInteractor creates a new list products interactor
func NewInteractor(repo repository.ProductRepository) *Interactor {
	return &Interactor{
		repo: repo,
	}
}

// Execute implements the Inport interface
func (i *Interactor) Execute(input Input) (Output, error) {
	// Validate input using struct validation
	if err := i.validateInput(input); err != nil {
		return Output{}, fmt.Errorf("validation failed: %w", err)
	}

	ctx := context.Background()
	page := input.GetPage()
	limit := input.GetLimit()

	// Get products with pagination
	products, total, err := i.repo.List(ctx, page, limit)
	if err != nil {
		return Output{}, fmt.Errorf("failed to list products: %w", err)
	}

	return Output{
		Products: products,
		Total:    total,
		Page:     page,
		Limit:    limit,
	}, nil
}

// validateInput validates the input parameters
func (i *Interactor) validateInput(input Input) error {
	if input.Page != nil && *input.Page < 1 {
		return fmt.Errorf("page must be greater than 0")
	}

	if input.Limit != nil && (*input.Limit < 1 || *input.Limit > 100) {
		return fmt.Errorf("limit must be between 1 and 100")
	}

	return nil
}
