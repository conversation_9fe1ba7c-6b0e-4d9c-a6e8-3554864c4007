package in

// Input represents the input data for listing products
type Input struct {
	Page  *int `json:"page,omitempty" validate:"omitempty,min=1"`
	Limit *int `json:"limit,omitempty" validate:"omitempty,min=1,max=100"`
}

// GetPage returns the page number with default value
func (i Input) GetPage() int {
	if i.Page == nil {
		return 1
	}
	return *i.Page
}

// GetLimit returns the limit with default value
func (i Input) GetLimit() int {
	if i.Limit == nil {
		return 10
	}
	return *i.Limit
}
