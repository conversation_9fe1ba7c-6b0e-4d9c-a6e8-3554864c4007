package out

import (
	"demo/usecase/catalog/products"
)

// ViewModel represents the presenter response for list products use case
type ViewModel struct {
	Success  bool                `json:"success"`
	Message  string              `json:"message,omitempty"`
	Products []*products.Product `json:"products,omitempty"`
	Total    int                 `json:"total,omitempty"`
	Page     int                 `json:"page,omitempty"`
	Limit    int                 `json:"limit,omitempty"`
	Error    string              `json:"error,omitempty"`
}
