package out

import (
	"demo/usecase/catalog/products/list/in"
)

// Presenter implements the list products result presentation logic
type Presenter struct{}

// NewPresenter creates a new list products presenter
func NewPresenter() *Presenter {
	return &Presenter{}
}

// Present implements the Outport interface
func (p *Presenter) Present(output in.Output, err error) (ViewModel, error) {
	if err != nil {
		return ViewModel{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	return ViewModel{
		Success:  true,
		Message:  "Products retrieved successfully",
		Products: output.Products,
		Total:    output.Total,
		Page:     output.Page,
		Limit:    output.Limit,
	}, nil
}
