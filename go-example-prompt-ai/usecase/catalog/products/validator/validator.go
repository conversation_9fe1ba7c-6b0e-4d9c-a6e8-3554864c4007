package validator

import (
	"errors"
	"strings"
)

// ProductValidator provides shared domain validation rules for products
type ProductValidator struct{}

// NewProductValidator creates a new ProductValidator instance
func NewProductValidator() *ProductValidator {
	return &ProductValidator{}
}

// ValidateProductName validates product name
func (v *ProductValidator) ValidateProductName(name string) error {
	name = strings.TrimSpace(name)
	if name == "" {
		return errors.New("product name cannot be empty")
	}
	if len(name) < 2 {
		return errors.New("product name must be at least 2 characters long")
	}
	if len(name) > 255 {
		return errors.New("product name cannot exceed 255 characters")
	}
	return nil
}

// ValidateProductDescription validates product description
func (v *ProductValidator) ValidateProductDescription(description string) error {
	description = strings.TrimSpace(description)
	if description == "" {
		return errors.New("product description cannot be empty")
	}
	if len(description) < 10 {
		return errors.New("product description must be at least 10 characters long")
	}
	if len(description) > 2000 {
		return errors.New("product description cannot exceed 2000 characters")
	}
	return nil
}

// ValidateProductPrice validates product price
func (v *ProductValidator) ValidateProductPrice(price float64) error {
	if price < 0 {
		return errors.New("product price cannot be negative")
	}
	if price > 999999.99 {
		return errors.New("product price cannot exceed 999999.99")
	}
	return nil
}

// ValidateProductQuantity validates product quantity
func (v *ProductValidator) ValidateProductQuantity(quantity int) error {
	if quantity < 0 {
		return errors.New("product quantity cannot be negative")
	}
	if quantity > 1000000 {
		return errors.New("product quantity cannot exceed 1000000")
	}
	return nil
}

// ValidateProductCategory validates product category
func (v *ProductValidator) ValidateProductCategory(category string) error {
	category = strings.TrimSpace(category)
	if category == "" {
		return errors.New("product category cannot be empty")
	}
	if len(category) < 2 {
		return errors.New("product category must be at least 2 characters long")
	}
	if len(category) > 100 {
		return errors.New("product category cannot exceed 100 characters")
	}
	return nil
}

// ValidateProductID validates product ID
func (v *ProductValidator) ValidateProductID(id string) error {
	id = strings.TrimSpace(id)
	if id == "" {
		return errors.New("product ID cannot be empty")
	}
	if len(id) < 1 {
		return errors.New("product ID must be at least 1 character long")
	}
	if len(id) > 50 {
		return errors.New("product ID cannot exceed 50 characters")
	}
	return nil
}
