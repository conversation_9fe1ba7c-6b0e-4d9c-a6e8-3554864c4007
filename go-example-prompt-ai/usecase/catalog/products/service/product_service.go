package service

import (
	createin "demo/usecase/catalog/products/create/in"
	createout "demo/usecase/catalog/products/create/out"
	deletein "demo/usecase/catalog/products/delete/in"
	deleteout "demo/usecase/catalog/products/delete/out"
	getin "demo/usecase/catalog/products/get/in"
	getout "demo/usecase/catalog/products/get/out"
	listin "demo/usecase/catalog/products/list/in"
	listout "demo/usecase/catalog/products/list/out"
	updatein "demo/usecase/catalog/products/update/in"
	updateout "demo/usecase/catalog/products/update/out"
)

// ProductService orchestrates use case execution for product operations
type ProductService struct {
	// Create use case ports
	createInport  createin.Inport
	createOutport createout.Outport

	// Update use case ports
	updateInport  updatein.Inport
	updateOutport updateout.Outport

	// Delete use case ports
	deleteInport  deletein.Inport
	deleteOutport deleteout.Outport

	// Get use case ports
	getInport  getin.Inport
	getOutport getout.Outport

	// List use case ports
	listInport  listin.Inport
	listOutport listout.Outport
}

// NewProductService creates a new ProductService instance
func NewProductService(
	createInport createin.Inport,
	createOutport createout.Outport,
	updateInport updatein.Inport,
	updateOutport updateout.Outport,
	deleteInport deletein.Inport,
	deleteOutport deleteout.Outport,
	getInport getin.Inport,
	getOutport getout.Outport,
	listInport listin.Inport,
	listOutport listout.Outport,
) *ProductService {
	return &ProductService{
		createInport:  createInport,
		createOutport: createOutport,
		updateInport:  updateInport,
		updateOutport: updateOutport,
		deleteInport:  deleteInport,
		deleteOutport: deleteOutport,
		getInport:     getInport,
		getOutport:    getOutport,
		listInport:    listInport,
		listOutport:   listOutport,
	}
}

// CreateProduct executes the create product use case
func (s *ProductService) CreateProduct(input createin.Input) (createout.ViewModel, error) {
	// Execute the use case through the inport
	inOutput, err := s.createInport.Execute(input)

	// Present the result through the outport
	return s.createOutport.Present(inOutput, err)
}

// UpdateProduct executes the update product use case
func (s *ProductService) UpdateProduct(input updatein.Input) (updateout.ViewModel, error) {
	// Execute the use case through the inport
	inOutput, err := s.updateInport.Execute(input)

	// Present the result through the outport
	return s.updateOutport.Present(inOutput, err)
}

// DeleteProduct executes the delete product use case
func (s *ProductService) DeleteProduct(input deletein.Input) (deleteout.ViewModel, error) {
	// Execute the use case through the inport
	inOutput, err := s.deleteInport.Execute(input)

	// Present the result through the outport
	return s.deleteOutport.Present(inOutput, err)
}

// GetProduct executes the get product use case
func (s *ProductService) GetProduct(input getin.Input) (getout.ViewModel, error) {
	// Execute the use case through the inport
	inOutput, err := s.getInport.Execute(input)

	// Present the result through the outport
	return s.getOutport.Present(inOutput, err)
}

// ListProducts executes the list products use case
func (s *ProductService) ListProducts(input listin.Input) (listout.ViewModel, error) {
	// Execute the use case through the inport
	inOutput, err := s.listInport.Execute(input)

	// Present the result through the outport
	return s.listOutport.Present(inOutput, err)
}
