package out

import (
	"demo/usecase/catalog/products/get/in"
)

// Presenter implements the get product result presentation logic
type Presenter struct{}

// NewPresenter creates a new get product presenter
func NewPresenter() *Presenter {
	return &Presenter{}
}

// Present implements the Outport interface
func (p *Presenter) Present(output in.Output, err error) (ViewModel, error) {
	if err != nil {
		return ViewModel{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	return ViewModel{
		Success: true,
		Message: "Product retrieved successfully",
		Product: output.Product,
	}, nil
}
