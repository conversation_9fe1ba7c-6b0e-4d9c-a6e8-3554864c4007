package in

import (
	"context"
	"demo/usecase/catalog/products/repository"
	"demo/usecase/catalog/products/validator"
	"fmt"
)

// Interactor implements the get product use case business logic
type Interactor struct {
	repo      repository.ProductRepository
	validator *validator.ProductValidator
}

// NewInteractor creates a new get product interactor
func NewInteractor(repo repository.ProductRepository, validator *validator.ProductValidator) *Interactor {
	return &Interactor{
		repo:      repo,
		validator: validator,
	}
}

// Execute implements the Inport interface
func (i *Interactor) Execute(input Input) (Output, error) {
	// Validate input using domain validator
	if err := i.validateInput(input); err != nil {
		return Output{}, fmt.Errorf("validation failed: %w", err)
	}

	ctx := context.Background()

	// Get the product
	product, err := i.repo.GetByID(ctx, input.ID)
	if err != nil {
		return Output{}, fmt.Errorf("failed to get product: %w", err)
	}

	return Output{Product: product}, nil
}

// validateInput validates the input using domain validation rules
func (i *Interactor) validateInput(input Input) error {
	if err := i.validator.ValidateProductID(input.ID); err != nil {
		return err
	}

	return nil
}
