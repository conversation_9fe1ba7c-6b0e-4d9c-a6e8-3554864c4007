package bunrepo

import (
	"time"

	"github.com/uptrace/bun"
)

// ProductEntity represents the database schema for products
type ProductEntity struct {
	bun.BaseModel `bun:"table:products,alias:p"`

	ID          string            `bun:"id,pk" json:"id"`
	Name        string            `bun:"name,notnull" json:"name"`
	Description string            `bun:"description,notnull" json:"description"`
	Price       float64           `bun:"price,notnull" json:"price"`
	Quantity    int               `bun:"quantity,notnull" json:"quantity"`
	Category    string            `bun:"category,notnull" json:"category"`
	Tags        []string          `bun:"tags,array" json:"tags"`
	Images      []string          `bun:"images,array" json:"images"`
	Attributes  map[string]string `bun:"attributes,type:jsonb" json:"attributes"`
	CreatedAt   time.Time         `bun:"created_at,nullzero,notnull,default:current_timestamp" json:"created_at"`
	UpdatedAt   time.Time         `bun:"updated_at,nullzero,notnull,default:current_timestamp" json:"updated_at"`
}
