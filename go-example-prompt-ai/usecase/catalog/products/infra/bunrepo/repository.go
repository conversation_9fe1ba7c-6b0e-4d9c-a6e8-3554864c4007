package bunrepo

import (
	"context"
	"database/sql"
	"demo/usecase/catalog/products"
	"demo/usecase/catalog/products/repository"
	"fmt"

	"github.com/uptrace/bun"
)

// Repository implements the ProductRepository interface using Bun ORM
type Repository struct {
	db     *bun.DB
	mapper *Mapper
}

// NewRepository creates a new Bun-based product repository
func NewRepository(db *bun.DB) repository.ProductRepository {
	return &Repository{
		db:     db,
		mapper: NewMapper(),
	}
}

// Create creates a new product in the database
func (r *Repository) Create(ctx context.Context, product *products.Product) error {
	entity := r.mapper.ToEntity(product)

	_, err := r.db.NewInsert().
		Model(entity).
		Exec(ctx)

	if err != nil {
		return fmt.Errorf("failed to create product: %w", err)
	}

	return nil
}

// Update updates an existing product in the database
func (r *Repository) Update(ctx context.Context, product *products.Product) error {
	entity := r.mapper.ToEntity(product)

	result, err := r.db.NewUpdate().
		Model(entity).
		WherePK().
		Exec(ctx)

	if err != nil {
		return fmt.Errorf("failed to update product: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("product with ID %s not found", product.ID)
	}

	return nil
}

// Delete deletes a product by ID from the database
func (r *Repository) Delete(ctx context.Context, id string) error {
	result, err := r.db.NewDelete().
		Model((*ProductEntity)(nil)).
		Where("id = ?", id).
		Exec(ctx)

	if err != nil {
		return fmt.Errorf("failed to delete product: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("product with ID %s not found", id)
	}

	return nil
}

// GetByID retrieves a product by ID from the database
func (r *Repository) GetByID(ctx context.Context, id string) (*products.Product, error) {
	entity := &ProductEntity{}

	err := r.db.NewSelect().
		Model(entity).
		Where("id = ?", id).
		Scan(ctx)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product with ID %s not found", id)
		}
		return nil, fmt.Errorf("failed to get product: %w", err)
	}

	return r.mapper.ToDomain(entity), nil
}

// List retrieves products with pagination from the database
func (r *Repository) List(ctx context.Context, page, limit int) ([]*products.Product, int, error) {
	// Calculate offset
	offset := (page - 1) * limit

	// Get total count
	total, err := r.db.NewSelect().
		Model((*ProductEntity)(nil)).
		Count(ctx)

	if err != nil {
		return nil, 0, fmt.Errorf("failed to count products: %w", err)
	}

	// Get products with pagination
	var entities []*ProductEntity
	err = r.db.NewSelect().
		Model(&entities).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Scan(ctx)

	if err != nil {
		return nil, 0, fmt.Errorf("failed to list products: %w", err)
	}

	products := r.mapper.ToDomainSlice(entities)
	return products, total, nil
}

// Exists checks if a product exists by ID in the database
func (r *Repository) Exists(ctx context.Context, id string) (bool, error) {
	count, err := r.db.NewSelect().
		Model((*ProductEntity)(nil)).
		Where("id = ?", id).
		Count(ctx)

	if err != nil {
		return false, fmt.Errorf("failed to check product existence: %w", err)
	}

	return count > 0, nil
}
