package bunrepo

import (
	"demo/usecase/catalog/products"
)

// <PERSON><PERSON> handles conversion between domain entities and database entities
type Mapper struct{}

// NewMapper creates a new mapper instance
func NewMapper() *Mapper {
	return &Mapper{}
}

// ToDomain converts a database entity to a domain entity
func (m *Mapper) ToDomain(entity *ProductEntity) *products.Product {
	if entity == nil {
		return nil
	}

	return &products.Product{
		ID:          entity.ID,
		Name:        entity.Name,
		Description: entity.Description,
		Price:       entity.Price,
		Quantity:    entity.Quantity,
		Category:    entity.Category,
		Tags:        entity.Tags,
		Images:      entity.Images,
		Attributes:  entity.Attributes,
		CreatedAt:   entity.CreatedAt,
		UpdatedAt:   entity.UpdatedAt,
	}
}

// ToEntity converts a domain entity to a database entity
func (m *Mapper) ToEntity(product *products.Product) *ProductEntity {
	if product == nil {
		return nil
	}

	return &ProductEntity{
		ID:          product.ID,
		Name:        product.Name,
		Description: product.Description,
		Price:       product.Price,
		Quantity:    product.Quantity,
		Category:    product.Category,
		Tags:        product.Tags,
		Images:      product.Images,
		Attributes:  product.Attributes,
		CreatedAt:   product.CreatedAt,
		UpdatedAt:   product.UpdatedAt,
	}
}

// ToDomainSlice converts a slice of database entities to domain entities
func (m *Mapper) ToDomainSlice(entities []*ProductEntity) []*products.Product {
	if entities == nil {
		return nil
	}

	result := make([]*products.Product, len(entities))
	for i, entity := range entities {
		result[i] = m.ToDomain(entity)
	}
	return result
}
