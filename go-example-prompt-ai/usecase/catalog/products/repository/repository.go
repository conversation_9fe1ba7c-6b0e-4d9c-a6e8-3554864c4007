package repository

import (
	"context"
	"demo/usecase/catalog/products"
)

// ProductRepository defines the contract for product data persistence
type ProductRepository interface {
	// Create creates a new product
	Create(ctx context.Context, product *products.Product) error
	
	// Update updates an existing product
	Update(ctx context.Context, product *products.Product) error
	
	// Delete deletes a product by ID
	Delete(ctx context.Context, id string) error
	
	// GetByID retrieves a product by ID
	GetByID(ctx context.Context, id string) (*products.Product, error)
	
	// List retrieves products with pagination
	List(ctx context.Context, page, limit int) ([]*products.Product, int, error)
	
	// Exists checks if a product exists by ID
	Exists(ctx context.Context, id string) (bool, error)
}
