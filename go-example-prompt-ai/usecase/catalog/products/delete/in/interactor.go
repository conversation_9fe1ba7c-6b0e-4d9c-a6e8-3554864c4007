package in

import (
	"context"
	"demo/usecase/catalog/products/repository"
	"demo/usecase/catalog/products/validator"
	"fmt"
)

// Interactor implements the delete product use case business logic
type Interactor struct {
	repo      repository.ProductRepository
	validator *validator.ProductValidator
}

// NewInteractor creates a new delete product interactor
func NewInteractor(repo repository.ProductRepository, validator *validator.ProductValidator) *Interactor {
	return &Interactor{
		repo:      repo,
		validator: validator,
	}
}

// Execute implements the Inport interface
func (i *Interactor) Execute(input Input) (Output, error) {
	// Validate input using domain validator
	if err := i.validateInput(input); err != nil {
		return Output{}, fmt.Errorf("validation failed: %w", err)
	}

	ctx := context.Background()

	// Check if product exists before deleting
	exists, err := i.repo.Exists(ctx, input.ID)
	if err != nil {
		return Output{}, fmt.Errorf("failed to check product existence: %w", err)
	}

	if !exists {
		return Output{}, fmt.Errorf("product with ID %s not found", input.ID)
	}

	// Delete the product
	if err := i.repo.Delete(ctx, input.ID); err != nil {
		return Output{}, fmt.Errorf("failed to delete product: %w", err)
	}

	return Output{Success: true}, nil
}

// validateInput validates the input using domain validation rules
func (i *Interactor) validateInput(input Input) error {
	if err := i.validator.ValidateProductID(input.ID); err != nil {
		return err
	}

	return nil
}
