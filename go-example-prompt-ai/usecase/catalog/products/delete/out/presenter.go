package out

import (
	"demo/usecase/catalog/products/delete/in"
)

// Presenter implements the delete product result presentation logic
type Presenter struct{}

// NewPresenter creates a new delete product presenter
func NewPresenter() *Presenter {
	return &Presenter{}
}

// Present implements the Outport interface
func (p *Presenter) Present(output in.Output, err error) (ViewModel, error) {
	if err != nil {
		return ViewModel{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	return ViewModel{
		Success: true,
		Message: "Product deleted successfully",
	}, nil
}
