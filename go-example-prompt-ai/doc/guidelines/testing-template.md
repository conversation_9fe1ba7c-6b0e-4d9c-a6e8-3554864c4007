# 🧪 Testing Template for Clean Architecture Use Cases

> ✅ This template defines comprehensive testing strategies for use cases following the **Clean Architecture** and **DDD** principles outlined in `usecase-template.md`.
> 
> 🎯 **Goal**: Achieve >80% test coverage with high-quality, maintainable tests that pass SonarQube quality gates.

---

## 🎯 **TESTING PRINCIPLES**

### **Core Requirements**
- ✅ **Table-driven tests** for all applicable scenarios
- ✅ **Parallel execution** when possible (`t.Parallel()`)
- ✅ **Isolation** - tests run independently
- ✅ **Order independence** - tests can run in any sequence
- ✅ **Repeatability** - multiple runs produce same results
- ✅ **Cross-platform** - works on any OS/architecture/Go version
- ✅ **SonarQube compliance** - passes default quality gates
- ✅ **>80% test coverage** across all components

### **Testing Layers**
1. **Unit Tests** - Individual components (interactor, presenter, validator)
2. **Integration Tests** - Component interactions within use case
3. **Service Tests** - End-to-end use case execution
4. **Repository Tests** - Database layer with test containers

---

## 🏗️ **TESTING STRUCTURE**

### **File Organization**
```bash
usecase/<domain>/
├── <usecase>/
│   ├── interactor_test.go         # Business logic tests
│   ├── presenter_test.go          # Presentation logic tests
│   ├── validator_test.go          # Input validation tests
│   └── integration_test.go        # Use case integration tests
├── service/
│   └── <entity>_service_test.go   # Service layer tests
├── infra/bunrepo/
│   ├── repository_test.go         # Repository implementation tests
│   └── testdata/                  # Test fixtures and data
│       ├── fixtures.sql
│       └── test_data.json
└── testutils/                     # Shared test utilities
    ├── mocks.go                   # Mock implementations
    ├── fixtures.go                # Test data builders
    └── assertions.go              # Custom assertions
```

---

## 📋 **SERVICE LAYER TESTING**

### **Template: `service/<entity>_service_test.go`**

```go
package service

import (
	"testing"
	"os"
	
	"github.com/samber/do"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

var injector *do.Injector

func TestMain(m *testing.M) {
	// Setup DI container with test dependencies
	injector = do.New()
	
	// Register test implementations (mocks/stubs)
	if err := RegisterTestDependencies(injector); err != nil {
		panic(err)
	}
	
	// Run tests
	code := m.Run()
	
	// Cleanup
	injector.Shutdown()
	os.Exit(code)
}

func TestProductService_CreateProduct(t *testing.T) {
	testCases := []struct {
		name        string
		setup       func(t *testing.T) error
		input       create.CreateProductInput
		verify      func(t *testing.T, input create.CreateProductInput, output create.CreateProductViewModel, err error) error
		shouldError bool
		errorType   string
	}{
		{
			name: "valid_input_creates_product_successfully",
			setup: func(t *testing.T) error {
				// Setup mocks for successful creation
				mockRepo := do.MustInvoke[*MockProductRepository](injector)
				mockRepo.On("Create", mock.Anything, mock.Anything).Return(nil)
				return nil
			},
			input: create.CreateProductInput{
				Name:        "Test Product",
				Description: "This is a test product description",
				Price:       9.99,
				Quantity:    10,
				Category:    "Test Category",
				Tags:        []string{"tag1", "tag2"},
				Images:      []string{"http://example.com/image1.jpg"},
				Attributes:  map[string]string{"attr1": "value1"},
			},
			verify: func(t *testing.T, input create.CreateProductInput, output create.CreateProductViewModel, err error) error {
				require.NoError(t, err)
				assert.True(t, output.Success)
				assert.NotEmpty(t, output.Product.ID)
				assert.Equal(t, input.Name, output.Product.Name)
				assert.Equal(t, input.Description, output.Product.Description)
				assert.Equal(t, input.Price, output.Product.Price)
				return nil
			},
			shouldError: false,
		},
		{
			name: "invalid_input_returns_validation_error",
			setup: func(t *testing.T) error {
				// No setup needed for validation errors
				return nil
			},
			input: create.CreateProductInput{
				Name:        "", // Invalid: empty name
				Description: "Valid description",
				Price:       9.99,
				Quantity:    10,
				Category:    "Test Category",
			},
			verify: func(t *testing.T, input create.CreateProductInput, output create.CreateProductViewModel, err error) error {
				require.Error(t, err)
				assert.Contains(t, err.Error(), "validation failed")
				assert.False(t, output.Success)
				return nil
			},
			shouldError: true,
			errorType:   "validation",
		},
		{
			name: "repository_error_returns_service_error",
			setup: func(t *testing.T) error {
				mockRepo := do.MustInvoke[*MockProductRepository](injector)
				mockRepo.On("Create", mock.Anything, mock.Anything).Return(errors.New("database error"))
				return nil
			},
			input: create.CreateProductInput{
				Name:        "Test Product",
				Description: "This is a test product description",
				Price:       9.99,
				Quantity:    10,
				Category:    "Test Category",
			},
			verify: func(t *testing.T, input create.CreateProductInput, output create.CreateProductViewModel, err error) error {
				require.Error(t, err)
				assert.Contains(t, err.Error(), "failed to create product")
				assert.False(t, output.Success)
				return nil
			},
			shouldError: true,
			errorType:   "repository",
		},
	}

	for _, tc := range testCases {
		tc := tc // Capture range variable
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			
			// Arrange
			service := do.MustInvoke[*ProductService](injector)
			require.NotNil(t, service)
			
			if err := tc.setup(t); err != nil {
				t.Fatalf("setup failed: %v", err)
			}
			
			// Act
			output, err := service.CreateProduct(tc.input)
			
			// Assert
			if tc.shouldError && err == nil {
				t.Fatal("expected error, but got nil")
			}
			if !tc.shouldError && err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			
			if err := tc.verify(t, tc.input, output, err); err != nil {
				t.Fatalf("verification failed: %v", err)
			}
		})
	}
}
```

---

## 🎨 **PRESENTER TESTING**

### **Template: `<usecase>/presenter_test.go`**

```go
package create

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPresenter_Present(t *testing.T) {
	testCases := []struct {
		name           string
		output         Output
		err            error
		expectedResult ViewModel
	}{
		{
			name: "successful_presentation",
			output: Output{
				Product: &products.Product{
					ID:          "test-id-123",
					Name:        "Test Product",
					Description: "Test Description",
					Price:       9.99,
					Quantity:    10,
					Category:    "Electronics",
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				},
			},
			err: nil,
			expectedResult: ViewModel{
				Success: true,
				Message: "Product created successfully",
				Product: &products.Product{
					ID:          "test-id-123",
					Name:        "Test Product",
					Description: "Test Description",
					Price:       9.99,
					Quantity:    10,
					Category:    "Electronics",
				},
			},
		},
		{
			name:   "error_presentation",
			output: Output{},
			err:    errors.New("validation failed: invalid input"),
			expectedResult: ViewModel{
				Success: false,
				Error:   "validation failed: invalid input",
			},
		},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			presenter := NewPresenter()

			// Act
			result, err := presenter.Present(tc.output, tc.err)

			// Assert
			require.NoError(t, err) // Presenter should not return errors
			assert.Equal(t, tc.expectedResult.Success, result.Success)
			assert.Equal(t, tc.expectedResult.Message, result.Message)
			assert.Equal(t, tc.expectedResult.Error, result.Error)

			if tc.expectedResult.Product != nil {
				require.NotNil(t, result.Product)
				assert.Equal(t, tc.expectedResult.Product.ID, result.Product.ID)
				assert.Equal(t, tc.expectedResult.Product.Name, result.Product.Name)
			}
		})
	}
}
```

---

## ✅ **VALIDATOR TESTING**

### **Template: `<usecase>/validator_test.go`**

```go
package create

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestValidator_ValidateInput(t *testing.T) {
	testCases := []struct {
		name          string
		input         Input
		expectedError string
	}{
		{
			name: "valid_input_passes_validation",
			input: Input{
				Name:        "Valid Product Name",
				Description: "This is a valid product description with enough characters",
				Price:       9.99,
				Quantity:    10,
				Category:    "Electronics",
				Tags:        []string{"tag1", "tag2"},
				Images:      []string{"http://example.com/image.jpg"},
				Attributes:  map[string]string{"color": "red"},
			},
		},
		{
			name: "empty_name_fails_validation",
			input: Input{
				Name:        "",
				Description: "Valid description",
				Price:       9.99,
				Quantity:    10,
				Category:    "Electronics",
			},
			expectedError: "Name",
		},
		{
			name: "short_description_fails_validation",
			input: Input{
				Name:        "Valid Name",
				Description: "Short",
				Price:       9.99,
				Quantity:    10,
				Category:    "Electronics",
			},
			expectedError: "Description",
		},
		{
			name: "negative_price_fails_validation",
			input: Input{
				Name:        "Valid Name",
				Description: "Valid description with enough characters",
				Price:       -1.0,
				Quantity:    10,
				Category:    "Electronics",
			},
			expectedError: "Price",
		},
		{
			name: "invalid_url_in_images_fails_validation",
			input: Input{
				Name:        "Valid Name",
				Description: "Valid description with enough characters",
				Price:       9.99,
				Quantity:    10,
				Category:    "Electronics",
				Images:      []string{"not-a-valid-url"},
			},
			expectedError: "Images",
		},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			validator := NewValidator()

			// Act
			err := validator.ValidateInput(tc.input)

			// Assert
			if tc.expectedError != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
```

---

## 🗄️ **REPOSITORY TESTING**

### **Template: `infra/bunrepo/repository_test.go`**

```go
package bunrepo

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/pgdialect"
	"github.com/uptrace/bun/driver/pgdriver"
)

var testDB *bun.DB

func TestMain(m *testing.M) {
	// Setup test database container
	ctx := context.Background()

	postgresContainer, err := postgres.RunContainer(ctx,
		testcontainers.WithImage("postgres:15-alpine"),
		postgres.WithDatabase("testdb"),
		postgres.WithUsername("testuser"),
		postgres.WithPassword("testpass"),
	)
	if err != nil {
		panic(err)
	}
	defer postgresContainer.Terminate(ctx)

	// Get connection string
	connStr, err := postgresContainer.ConnectionString(ctx, "sslmode=disable")
	if err != nil {
		panic(err)
	}

	// Setup database connection
	sqldb := sql.OpenDB(pgdriver.NewConnector(pgdriver.WithDSN(connStr)))
	testDB = bun.NewDB(sqldb, pgdialect.New())

	// Create tables
	if err := createTestTables(ctx); err != nil {
		panic(err)
	}

	// Run tests
	code := m.Run()

	// Cleanup
	testDB.Close()
	os.Exit(code)
}

func TestRepository_Create(t *testing.T) {
	testCases := []struct {
		name          string
		product       *products.Product
		expectedError string
	}{
		{
			name: "successful_creation",
			product: &products.Product{
				ID:          "test-id-123",
				Name:        "Test Product",
				Description: "Test Description",
				Price:       9.99,
				Quantity:    10,
				Category:    "Electronics",
				Tags:        []string{"tag1", "tag2"},
				Images:      []string{"http://example.com/image.jpg"},
				Attributes:  map[string]string{"color": "red"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
		},
		{
			name: "duplicate_id_fails",
			product: &products.Product{
				ID:          "duplicate-id",
				Name:        "Duplicate Product",
				Description: "This should fail due to duplicate ID",
				Price:       19.99,
				Quantity:    5,
				Category:    "Electronics",
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			expectedError: "duplicate key",
		},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			// Note: Repository tests may not always be parallel due to database state

			// Arrange
			ctx := context.Background()
			repo := NewRepository(testDB)

			// Setup test data if needed
			if tc.name == "duplicate_id_fails" {
				// Pre-insert a product with the same ID
				existingProduct := &products.Product{
					ID:          "duplicate-id",
					Name:        "Existing Product",
					Description: "Already exists",
					Price:       5.99,
					Quantity:    1,
					Category:    "Test",
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}
				_ = repo.Create(ctx, existingProduct)
			}

			// Act
			err := repo.Create(ctx, tc.product)

			// Assert
			if tc.expectedError != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError)
			} else {
				require.NoError(t, err)

				// Verify the product was created
				retrieved, err := repo.GetByID(ctx, tc.product.ID)
				require.NoError(t, err)
				assert.Equal(t, tc.product.Name, retrieved.Name)
				assert.Equal(t, tc.product.Price, retrieved.Price)
			}

			// Cleanup
			cleanupTestData(t, ctx, tc.product.ID)
		})
	}
}

func cleanupTestData(t *testing.T, ctx context.Context, productID string) {
	_, err := testDB.NewDelete().
		Model((*ProductEntity)(nil)).
		Where("id = ?", productID).
		Exec(ctx)
	if err != nil {
		t.Logf("cleanup failed for product %s: %v", productID, err)
	}
}
```

---

## 🛠️ **TEST UTILITIES**

### **Mock Implementations: `testutils/mocks.go`**

```go
package testutils

import (
	"context"
	"demo/usecase/catalog/products"

	"github.com/stretchr/testify/mock"
)

// MockProductRepository provides a mock implementation of ProductRepository
type MockProductRepository struct {
	mock.Mock
}

func (m *MockProductRepository) Create(ctx context.Context, product *products.Product) error {
	args := m.Called(ctx, product)
	return args.Error(0)
}

func (m *MockProductRepository) Update(ctx context.Context, product *products.Product) error {
	args := m.Called(ctx, product)
	return args.Error(0)
}

func (m *MockProductRepository) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockProductRepository) GetByID(ctx context.Context, id string) (*products.Product, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*products.Product), args.Error(1)
}

func (m *MockProductRepository) List(ctx context.Context, page, limit int) ([]*products.Product, int, error) {
	args := m.Called(ctx, page, limit)
	return args.Get(0).([]*products.Product), args.Int(1), args.Error(2)
}

func (m *MockProductRepository) Exists(ctx context.Context, id string) (bool, error) {
	args := m.Called(ctx, id)
	return args.Bool(0), args.Error(1)
}

// MockProductValidator provides a mock implementation of ProductValidator
type MockProductValidator struct {
	mock.Mock
}

func (m *MockProductValidator) ValidateProductName(name string) error {
	args := m.Called(name)
	return args.Error(0)
}

func (m *MockProductValidator) ValidateProductDescription(description string) error {
	args := m.Called(description)
	return args.Error(0)
}

func (m *MockProductValidator) ValidateProductPrice(price float64) error {
	args := m.Called(price)
	return args.Error(0)
}

func (m *MockProductValidator) ValidateProductQuantity(quantity int) error {
	args := m.Called(quantity)
	return args.Error(0)
}

func (m *MockProductValidator) ValidateProductCategory(category string) error {
	args := m.Called(category)
	return args.Error(0)
}

func (m *MockProductValidator) ValidateProductID(id string) error {
	args := m.Called(id)
	return args.Error(0)
}
```

### **Test Fixtures: `testutils/fixtures.go`**

```go
package testutils

import (
	"time"
	"demo/usecase/catalog/products"
	"demo/usecase/catalog/products/create/in"
)

// ProductBuilder provides a fluent interface for building test products
type ProductBuilder struct {
	product *products.Product
}

func NewProductBuilder() *ProductBuilder {
	return &ProductBuilder{
		product: &products.Product{
			ID:          "test-product-id",
			Name:        "Test Product",
			Description: "This is a test product description",
			Price:       9.99,
			Quantity:    10,
			Category:    "Electronics",
			Tags:        []string{"test", "product"},
			Images:      []string{"http://example.com/image.jpg"},
			Attributes:  map[string]string{"color": "red"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}
}

func (b *ProductBuilder) WithID(id string) *ProductBuilder {
	b.product.ID = id
	return b
}

func (b *ProductBuilder) WithName(name string) *ProductBuilder {
	b.product.Name = name
	return b
}

func (b *ProductBuilder) WithPrice(price float64) *ProductBuilder {
	b.product.Price = price
	return b
}

func (b *ProductBuilder) WithQuantity(quantity int) *ProductBuilder {
	b.product.Quantity = quantity
	return b
}

func (b *ProductBuilder) Build() *products.Product {
	return b.product
}

// CreateInputBuilder provides a fluent interface for building create inputs
type CreateInputBuilder struct {
	input *create.Input
}

func NewCreateInputBuilder() *CreateInputBuilder {
	return &CreateInputBuilder{
		input: &create.Input{
			Name:        "Test Product",
			Description: "This is a test product description",
			Price:       9.99,
			Quantity:    10,
			Category:    "Electronics",
			Tags:        []string{"test", "product"},
			Images:      []string{"http://example.com/image.jpg"},
			Attributes:  map[string]string{"color": "red"},
		},
	}
}

func (b *CreateInputBuilder) WithName(name string) *CreateInputBuilder {
	b.input.Name = name
	return b
}

func (b *CreateInputBuilder) WithInvalidName() *CreateInputBuilder {
	b.input.Name = ""
	return b
}

func (b *CreateInputBuilder) WithPrice(price float64) *CreateInputBuilder {
	b.input.Price = price
	return b
}

func (b *CreateInputBuilder) Build() create.Input {
	return *b.input
}
```

---

## 📊 **SONARQUBE COMPLIANCE**

### **Quality Gate Requirements**
- ✅ **Code Coverage**: >80% line coverage
- ✅ **Duplicated Lines**: <3% duplication
- ✅ **Maintainability Rating**: A (no code smells)
- ✅ **Reliability Rating**: A (no bugs)
- ✅ **Security Rating**: A (no vulnerabilities)
- ✅ **Cognitive Complexity**: <15 per function

### **Best Practices for SonarQube**

```go
// ✅ Good: Clear test names that describe behavior
func TestInteractor_Execute_WithValidInput_CreatesProductSuccessfully(t *testing.T) {
	// Test implementation
}

// ❌ Bad: Vague test names
func TestInteractor_Execute(t *testing.T) {
	// Test implementation
}

// ✅ Good: Proper error handling and assertions
func TestCreateProduct_WithInvalidInput_ReturnsValidationError(t *testing.T) {
	// Arrange
	input := create.Input{Name: ""} // Invalid input

	// Act
	output, err := service.CreateProduct(input)

	// Assert
	require.Error(t, err)
	assert.Contains(t, err.Error(), "validation failed")
	assert.False(t, output.Success)
}

// ✅ Good: Test isolation with proper cleanup
func TestRepository_Create_WithValidProduct_StoresSuccessfully(t *testing.T) {
	// Arrange
	ctx := context.Background()
	product := testutils.NewProductBuilder().Build()

	// Act
	err := repo.Create(ctx, product)

	// Assert
	require.NoError(t, err)

	// Cleanup
	defer func() {
		_ = repo.Delete(ctx, product.ID)
	}()
}
```

---

## 🎯 **COVERAGE TARGETS**

### **Component Coverage Goals**
- **Interactors**: >90% (core business logic)
- **Presenters**: >85% (formatting logic)
- **Validators**: >95% (validation rules)
- **Repositories**: >80% (database operations)
- **Services**: >85% (orchestration logic)

### **Coverage Commands**
```bash
# Run tests with coverage
go test -v -race -coverprofile=coverage.out ./...

# Generate HTML coverage report
go tool cover -html=coverage.out -o coverage.html

# Check coverage percentage
go tool cover -func=coverage.out | grep total

# SonarQube compatible coverage
go test -v -race -coverprofile=coverage.out -covermode=atomic ./...
```

---

## 🚀 **CONTINUOUS INTEGRATION**

### **GitHub Actions Example**
```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go-version: [1.21, 1.22]

    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}

    - name: Run tests
      run: |
        go test -v -race -coverprofile=coverage.out -covermode=atomic ./...
        go tool cover -func=coverage.out | grep total

    - name: SonarQube Scan
      uses: sonarqube-quality-gate-action@master
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
```

---

## ✅ **TESTING CHECKLIST**

### **Before Committing**
- [ ] All tests pass locally
- [ ] Coverage >80% overall
- [ ] No race conditions (`go test -race`)
- [ ] Tests run in parallel where possible
- [ ] Mock expectations are verified
- [ ] Test data is cleaned up
- [ ] SonarQube quality gate passes

### **Code Review Checklist**
- [ ] Test names clearly describe behavior
- [ ] Edge cases are covered
- [ ] Error scenarios are tested
- [ ] Mocks are properly configured
- [ ] No test dependencies between tests
- [ ] Proper use of table-driven tests
- [ ] Integration tests cover happy path

---

**🎯 Result**: Comprehensive, maintainable test suite with >80% coverage that passes SonarQube quality gates and supports reliable CI/CD pipelines.**
```
```

---

## 🔧 **INTERACTOR TESTING**

### **Template: `<usecase>/interactor_test.go`**

```go
package create

import (
	"context"
	"testing"
	"time"
	
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func TestInteractor_Execute(t *testing.T) {
	testCases := []struct {
		name           string
		setupMocks     func(*MockProductRepository, *MockProductValidator)
		input          Input
		expectedOutput Output
		expectedError  string
	}{
		{
			name: "successful_product_creation",
			setupMocks: func(repo *MockProductRepository, validator *MockProductValidator) {
				validator.On("ValidateProductName", "Test Product").Return(nil)
				validator.On("ValidateProductDescription", mock.Anything).Return(nil)
				validator.On("ValidateProductPrice", 9.99).Return(nil)
				validator.On("ValidateProductQuantity", 10).Return(nil)
				validator.On("ValidateProductCategory", "Electronics").Return(nil)
				
				repo.On("Create", mock.Anything, mock.MatchedBy(func(p *products.Product) bool {
					return p.Name == "Test Product" && p.Price == 9.99
				})).Return(nil)
			},
			input: Input{
				Name:        "Test Product",
				Description: "A great test product for testing purposes",
				Price:       9.99,
				Quantity:    10,
				Category:    "Electronics",
			},
			expectedOutput: Output{
				Product: &products.Product{
					Name:        "Test Product",
					Description: "A great test product for testing purposes",
					Price:       9.99,
					Quantity:    10,
					Category:    "Electronics",
				},
			},
		},
		{
			name: "validation_error_invalid_name",
			setupMocks: func(repo *MockProductRepository, validator *MockProductValidator) {
				validator.On("ValidateProductName", "").Return(errors.New("product name cannot be empty"))
			},
			input: Input{
				Name:        "",
				Description: "Valid description",
				Price:       9.99,
				Quantity:    10,
				Category:    "Electronics",
			},
			expectedError: "validation failed: product name cannot be empty",
		},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			
			// Arrange
			mockRepo := &MockProductRepository{}
			mockValidator := &MockProductValidator{}
			tc.setupMocks(mockRepo, mockValidator)
			
			interactor := NewInteractor(mockRepo, mockValidator)
			
			// Act
			output, err := interactor.Execute(tc.input)
			
			// Assert
			if tc.expectedError != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedError)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tc.expectedOutput.Product.Name, output.Product.Name)
				assert.Equal(t, tc.expectedOutput.Product.Price, output.Product.Price)
				assert.NotEmpty(t, output.Product.ID)
				assert.WithinDuration(t, time.Now(), output.Product.CreatedAt, time.Second)
			}
			
			// Verify all mock expectations
			mockRepo.AssertExpectations(t)
			mockValidator.AssertExpectations(t)
		})
	}
}
```
