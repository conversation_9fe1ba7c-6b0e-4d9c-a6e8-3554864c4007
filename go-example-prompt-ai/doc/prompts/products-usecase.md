# � AI Agent Prompt: Generate Complete CRUD for Products Domain

> **Objective**: Generate a complete, production-ready CRUD system for products following Clean Architecture and DDD principles.
>
> **Target**: AI agents (especially Augment Code) to create a fully functional product management system.

---

## 🎯 **WHAT TO BUILD**

Create a complete **catalog/products** domain with full CRUD operations:
- ✅ **CREATE** - Add new products to catalog
- ✅ **READ** - Get single product & list products with pagination
- ✅ **UPDATE** - Modify existing product data
- ✅ **DELETE** - Remove products from catalog

---

## 📋 **REQUIREMENTS**

### **Architecture Requirements**
- Follow the template in `guidelines/usecase-template.md` **EXACTLY**
- Use Clean Architecture with clear layer separation
- Apply Domain-Driven Design (DDD) principles
- Each use case must be self-contained and modular

### **Technical Stack**
- **Language**: Go
- **ORM**: Bun (github.com/uptrace/bun)
- **Validation**: go-playground/validator/v10
- **Module**: `demo` (as defined in go.mod)

### **Product Data Model**
```go
type Product struct {
    ID          string            `json:"id"`           // Auto-generated UUID
    Name        string            `json:"name"`         // Required, 2-255 chars
    Description string            `json:"description"`  // Required, 10-2000 chars
    Price       float64           `json:"price"`        // Required, >= 0, <= 999999.99
    Quantity    int               `json:"quantity"`     // Required, >= 0, <= 1000000
    Category    string            `json:"category"`     // Required, 2-100 chars
    Tags        []string          `json:"tags"`         // Optional, each 1-50 chars
    Images      []string          `json:"images"`       // Optional, valid URLs
    Attributes  map[string]string `json:"attributes"`   // Optional, key/value pairs
    CreatedAt   time.Time         `json:"created_at"`   // Auto-generated
    UpdatedAt   time.Time         `json:"updated_at"`   // Auto-updated
}
```

---

## 🏗️ **IMPLEMENTATION SPECIFICATION**

### **1. CREATE Use Case**
**Business Logic**: Create a new product with validation and unique ID generation.

**Input**:
- `name` (string, required, 2-255 chars)
- `description` (string, required, 10-2000 chars)
- `price` (float64, required, 0-999999.99)
- `quantity` (int, required, 0-1000000)
- `category` (string, required, 2-100 chars)
- `tags` ([]string, optional, each 1-50 chars)
- `images` ([]string, optional, valid URLs)
- `attributes` (map[string]string, optional)

**Output**: Complete Product entity with generated ID and timestamps

**Validation**: Domain validation + struct validation with meaningful error messages

---

### **2. UPDATE Use Case**
**Business Logic**: Update existing product with partial data (only provided fields).

**Input**:
- `id` (string, required, existing product ID)
- `name` (string, optional, 2-255 chars)
- `description` (string, optional, 10-2000 chars)
- `price` (float64, optional, 0-999999.99)
- `quantity` (int, optional, 0-1000000)
- `category` (string, optional, 2-100 chars)
- `tags` ([]string, optional)
- `images` ([]string, optional)
- `attributes` (map[string]string, optional)

**Output**: Updated Product entity

**Logic**: Merge provided fields with existing data, update timestamp

---

### **3. DELETE Use Case**
**Business Logic**: Remove product from catalog with existence check.

**Input**:
- `id` (string, required, existing product ID)

**Output**:
- `success` (bool, deletion confirmation)

**Logic**: Verify product exists before deletion, return meaningful errors

---

### **4. GET Use Case**
**Business Logic**: Retrieve single product by ID.

**Input**:
- `id` (string, required, existing product ID)

**Output**: Complete Product entity

**Logic**: Return product or meaningful "not found" error

---

### **5. LIST Use Case**
**Business Logic**: Retrieve products with pagination support.

**Input**:
- `page` (int, optional, default: 1, min: 1)
- `limit` (int, optional, default: 10, min: 1, max: 100)

**Output**:
- `products` ([]Product, array of products)
- `total` (int, total count for pagination)
- `page` (int, current page)
- `limit` (int, items per page)

**Logic**: Implement proper pagination with total count

---

## 🎯 **DELIVERABLES CHECKLIST**

### **Core Domain Components**
- [ ] `entity.go` - Product domain entity
- [ ] `repository/repository.go` - Repository interface
- [ ] `validator/validator.go` - Shared domain validation
- [ ] `service/product_service.go` - Application service orchestrator

### **Per Use Case (create, update, delete, get, list)**
- [ ] `{usecase}/input.go` - Input DTO with validation tags
- [ ] `{usecase}/output.go` - Use case output DTO
- [ ] `{usecase}/inport.go` - Use case interface
- [ ] `{usecase}/interactor.go` - Business logic implementation
- [ ] `{usecase}/validator.go` - Input validation
- [ ] `{usecase}/out/output.go` - Presenter response DTO
- [ ] `{usecase}/out/outport.go` - Presenter interface
- [ ] `{usecase}/out/presenter.go` - Presenter implementation

### **Infrastructure Layer**
- [ ] `infra/bunrepo/entity.go` - Database schema with Bun tags
- [ ] `infra/bunrepo/mapper.go` - Domain/DB entity mapping
- [ ] `infra/bunrepo/repository.go` - Bun repository implementation

---

## 🔧 **IMPLEMENTATION GUIDELINES**

### **For AI Agents**
1. **Read the template first**: Always check `guidelines/usecase-template.md` before starting
2. **Follow naming conventions**: Use exact naming from template (Inport, Outport, Interactor, etc.)
3. **Generate complete structure**: Don't skip any files or components
4. **Use proper imports**: Reference the correct module name (`demo`)
5. **Add comprehensive validation**: Both domain-level and struct-level validation
6. **Handle errors properly**: Meaningful error messages with context
7. **Implement all CRUD operations**: Don't leave any use case incomplete

### **Code Quality Standards**
- Use meaningful variable names and comments
- Implement proper error handling with wrapped errors
- Add validation for all business rules
- Use consistent response formats across presenters
- Implement proper pagination logic
- Generate unique IDs for new entities
- Update timestamps appropriately

### **Testing Considerations**
- Structure code for easy unit testing
- Separate business logic from infrastructure
- Use dependency injection for testability
- Make each component independently testable

---

## 🚨 **CRITICAL SUCCESS FACTORS**

1. **Complete Implementation**: All 5 CRUD operations must be fully implemented
2. **Architecture Compliance**: Must follow the template structure exactly
3. **Validation Coverage**: All business rules must be validated
4. **Error Handling**: Comprehensive error handling with meaningful messages
5. **Database Integration**: Proper Bun ORM integration with mapping
6. **Modularity**: Each use case must be self-contained and reusable

---

**🎯 Expected Result**: A production-ready, fully-tested product CRUD system that can be immediately integrated into any Go application following Clean Architecture principles.**