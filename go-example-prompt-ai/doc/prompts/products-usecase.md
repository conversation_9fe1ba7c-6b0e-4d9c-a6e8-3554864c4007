# 📘 Use Case Specification for `catalog/products`

> ✅ This document defines the business-driven use cases for the `catalog/products` domain.  
> 🧩 Each use case must follow the architectural conventions defined in  
> `doc/guidelines/usecase-template.md`.

---

## 📂 Domain: `catalog/products`

This domain manages all product-related operations for the catalog, including creation, updates, deletions, retrieval, and listing.

Each use case must:

- ✅ Follow Clean Architecture and DDD principles.
    
- ✅ Define a clear input/output contract.
    
- ✅ Be structured according to the use case template guideline.
    
- ✅ Include a `register.go` to bind all components to the DI container (`samber/do`).
    

---

## 📌 Use Case: `create`

### 🎯 Business Description

Create a new product in the catalog with basic metadata, tags, images, and custom attributes.

### 📥 Input DTO

- `name` (string, required)
    
- `description` (string, required)
    
- `price` (float64, required)
    
- `quantity` (int, required)
    
- `category` (string, required)
    
- `tags` ([]string, optional)
    
- `images` ([]string, optional)
    
- `attributes` (map[string]string, optional)
    

### 📤 Output DTO

- `product` (Product)
    

### 🛠 Technical Tasks

- Define CreateProductInput and CreateProductOutput
- Define CreateProductInport interface
- Implement CreateProductInteractor
- Define ProductEntity in infra/bunrepo/entity.go
- Map CreateProductInput to ProductEntity in mapper.go
- Implement presenter returning CreateProductViewModel
- Write validator.go using go-playground/validator
- Register interactor, presenter, and validator in register.go