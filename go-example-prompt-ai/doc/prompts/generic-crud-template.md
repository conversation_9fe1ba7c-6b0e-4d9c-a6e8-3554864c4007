# 🚀 AI Agent Prompt: Generate Complete CRUD System

> **Objective**: Generate a complete, production-ready CRUD system following Clean Architecture and DDD principles.
> 
> **Target**: AI agents (especially Augment Code) to create fully functional domain management systems.

---

## 🎯 **WHAT TO BUILD**

Create a complete **{DOMAIN_PATH}** domain with full CRUD operations:
- ✅ **CREATE** - Add new {ENTITY_NAME} to system
- ✅ **READ** - Get single {ENTITY_NAME} & list with pagination  
- ✅ **UPDATE** - Modify existing {ENTITY_NAME} data
- ✅ **DELETE** - Remove {ENTITY_NAME} from system

---

## 📋 **REQUIREMENTS**

### **Architecture Requirements**
- Follow the template in `guidelines/usecase-template.md` **EXACTLY**
- Use Clean Architecture with clear layer separation
- Apply Domain-Driven Design (DDD) principles
- Each use case must be self-contained and modular

### **Technical Stack**
- **Language**: Go
- **ORM**: Bun (github.com/uptrace/bun) 
- **Validation**: go-playground/validator/v10
- **Module**: `{MODULE_NAME}` (as defined in go.mod)

### **{ENTITY_NAME} Data Model**
```go
type {ENTITY_NAME} struct {
    ID        string    `json:"id"`         // Auto-generated UUID
    // Add your specific fields here with validation rules
    CreatedAt time.Time `json:"created_at"` // Auto-generated
    UpdatedAt time.Time `json:"updated_at"` // Auto-updated
}
```

---

## 🏗️ **IMPLEMENTATION SPECIFICATION**

### **1. CREATE Use Case**
**Business Logic**: Create a new {ENTITY_NAME} with validation and unique ID generation.

**Input**: Define required and optional fields based on your domain needs

**Output**: Complete {ENTITY_NAME} entity with generated ID and timestamps

**Validation**: Domain validation + struct validation with meaningful error messages

---

### **2. UPDATE Use Case**  
**Business Logic**: Update existing {ENTITY_NAME} with partial data (only provided fields).

**Input**: 
- `id` (string, required, existing {ENTITY_NAME} ID)
- Other fields (optional, based on your domain)

**Output**: Updated {ENTITY_NAME} entity

**Logic**: Merge provided fields with existing data, update timestamp

---

### **3. DELETE Use Case**
**Business Logic**: Remove {ENTITY_NAME} from system with existence check.

**Input**: 
- `id` (string, required, existing {ENTITY_NAME} ID)

**Output**: 
- `success` (bool, deletion confirmation)

**Logic**: Verify {ENTITY_NAME} exists before deletion, return meaningful errors

---

### **4. GET Use Case**
**Business Logic**: Retrieve single {ENTITY_NAME} by ID.

**Input**:
- `id` (string, required, existing {ENTITY_NAME} ID)  

**Output**: Complete {ENTITY_NAME} entity

**Logic**: Return {ENTITY_NAME} or meaningful "not found" error

---

### **5. LIST Use Case**
**Business Logic**: Retrieve {ENTITY_NAME}s with pagination support.

**Input**:
- `page` (int, optional, default: 1, min: 1)
- `limit` (int, optional, default: 10, min: 1, max: 100)

**Output**:
- `{ENTITY_NAME_LOWER}s` ([]Product, array of {ENTITY_NAME}s)
- `total` (int, total count for pagination)
- `page` (int, current page)  
- `limit` (int, items per page)

**Logic**: Implement proper pagination with total count

---

## 🎯 **DELIVERABLES CHECKLIST**

### **Core Domain Components**
- [ ] `entity.go` - {ENTITY_NAME} domain entity
- [ ] `repository/repository.go` - Repository interface  
- [ ] `validator/validator.go` - Shared domain validation
- [ ] `service/{ENTITY_NAME_LOWER}_service.go` - Application service orchestrator

### **Per Use Case (create, update, delete, get, list)**
- [ ] `{usecase}/in/input.go` - Input DTO with validation tags
- [ ] `{usecase}/in/output.go` - Use case output DTO
- [ ] `{usecase}/in/inport.go` - Use case interface
- [ ] `{usecase}/in/interactor.go` - Business logic implementation
- [ ] `{usecase}/in/validator/validator.go` - Input validation
- [ ] `{usecase}/out/output.go` - Presenter response DTO  
- [ ] `{usecase}/out/outport.go` - Presenter interface
- [ ] `{usecase}/out/presenter.go` - Presenter implementation

### **Infrastructure Layer**
- [ ] `infra/bunrepo/entity.go` - Database schema with Bun tags
- [ ] `infra/bunrepo/mapper.go` - Domain/DB entity mapping
- [ ] `infra/bunrepo/repository.go` - Bun repository implementation

---

## 🔧 **IMPLEMENTATION GUIDELINES**

### **For AI Agents**
1. **Read the template first**: Always check `guidelines/usecase-template.md` before starting
2. **Follow naming conventions**: Use exact naming from template (Inport, Outport, Interactor, etc.)
3. **Generate complete structure**: Don't skip any files or components
4. **Use proper imports**: Reference the correct module name
5. **Add comprehensive validation**: Both domain-level and struct-level validation
6. **Handle errors properly**: Meaningful error messages with context
7. **Implement all CRUD operations**: Don't leave any use case incomplete

### **Code Quality Standards**
- Use meaningful variable names and comments
- Implement proper error handling with wrapped errors
- Add validation for all business rules
- Use consistent response formats across presenters
- Implement proper pagination logic
- Generate unique IDs for new entities
- Update timestamps appropriately

### **Testing Considerations** 
- Structure code for easy unit testing
- Separate business logic from infrastructure
- Use dependency injection for testability
- Make each component independently testable

---

## 🚨 **CRITICAL SUCCESS FACTORS**

1. **Complete Implementation**: All 5 CRUD operations must be fully implemented
2. **Architecture Compliance**: Must follow the template structure exactly  
3. **Validation Coverage**: All business rules must be validated
4. **Error Handling**: Comprehensive error handling with meaningful messages
5. **Database Integration**: Proper Bun ORM integration with mapping
6. **Modularity**: Each use case must be self-contained and reusable

---

**🎯 Expected Result**: A production-ready, fully-tested {ENTITY_NAME} CRUD system that can be immediately integrated into any Go application following Clean Architecture principles.**

---

## 📝 **HOW TO USE THIS TEMPLATE**

Replace the following placeholders with your specific values:
- `{DOMAIN_PATH}` - e.g., `catalog/products`, `user/profiles`, `billing/invoices`
- `{ENTITY_NAME}` - e.g., `Product`, `User`, `Invoice` (PascalCase)
- `{ENTITY_NAME_LOWER}` - e.g., `product`, `user`, `invoice` (lowercase)
- `{MODULE_NAME}` - Your Go module name from go.mod

Then customize the data model and validation rules for your specific domain needs.
